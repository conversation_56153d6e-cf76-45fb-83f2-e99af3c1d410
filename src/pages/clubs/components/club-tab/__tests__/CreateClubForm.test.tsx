// Test the validateImageFile function in isolation
const ALLOWED_IMAGE_TYPES = ['image/png', 'image/jpeg'] as const;
const ALLOWED_EXTENSIONS = ['.png', '.jpg', '.jpeg'] as const;
const MAX_IMAGE_SIZE_BYTES = 5 * 1024 * 1024;

function validateImageFile(
  imageFile: File | null | undefined,
  options: { required: boolean }
): string | null {
  // Check if image is required
  if (options.required && !imageFile) {
    return 'Image is required';
  }

  // If no file provided and not required, validation passes
  if (!imageFile) return null;

  // Check if it's a valid File object
  if (!(imageFile instanceof File)) {
    return 'Please select a valid image file';
  }

  // Check file size
  if (imageFile.size > MAX_IMAGE_SIZE_BYTES) {
    return 'Image size must be less than 5MB';
  }

  // Check file extension (case-insensitive)
  const fileName = imageFile.name.toLowerCase();
  const hasValidExtension = ALLOWED_EXTENSIONS.some((ext) => fileName.endsWith(ext));

  if (!hasValidExtension) {
    return 'Invalid file type. Please select a PNG or JPG image.';
  }

  // Check MIME type
  const fileType = imageFile.type.toLowerCase();
  if (!ALLOWED_IMAGE_TYPES.includes(fileType as any)) {
    return 'Invalid file type. Please select a PNG or JPG image.';
  }

  return null;
}

// Mock File constructor for testing
class MockFile extends File {
  constructor(name: string, type: string, size: number = 1000) {
    super([''], name, { type });
    Object.defineProperty(this, 'size', { value: size });
  }
}

describe('CreateClubForm Image Validation', () => {
  describe('validateImageFile', () => {
    it('should require image when required option is true', () => {
      const result = validateImageFile(null, { required: true });
      expect(result).toBe('Image is required');
    });

    it('should allow null when required option is false', () => {
      const result = validateImageFile(null, { required: false });
      expect(result).toBe(null);
    });

    it('should accept valid PNG files', () => {
      const pngFile = new MockFile('test.png', 'image/png');
      const result = validateImageFile(pngFile, { required: true });
      expect(result).toBe(null);
    });

    it('should accept valid JPG files', () => {
      const jpgFile = new MockFile('test.jpg', 'image/jpeg');
      const result = validateImageFile(jpgFile, { required: true });
      expect(result).toBe(null);
    });

    it('should accept valid JPEG files', () => {
      const jpegFile = new MockFile('test.jpeg', 'image/jpeg');
      const result = validateImageFile(jpegFile, { required: true });
      expect(result).toBe(null);
    });

    it('should accept case-insensitive extensions', () => {
      const pngFile = new MockFile('test.PNG', 'image/png');
      const jpgFile = new MockFile('test.JPG', 'image/jpeg');
      const jpegFile = new MockFile('test.JPEG', 'image/jpeg');

      expect(validateImageFile(pngFile, { required: true })).toBe(null);
      expect(validateImageFile(jpgFile, { required: true })).toBe(null);
      expect(validateImageFile(jpegFile, { required: true })).toBe(null);
    });

    it('should reject invalid file extensions', () => {
      const gifFile = new MockFile('test.gif', 'image/gif');
      const result = validateImageFile(gifFile, { required: true });
      expect(result).toBe('Invalid file type. Please select a PNG or JPG image.');
    });

    it('should reject invalid MIME types', () => {
      const invalidFile = new MockFile('test.png', 'image/gif');
      const result = validateImageFile(invalidFile, { required: true });
      expect(result).toBe('Invalid file type. Please select a PNG or JPG image.');
    });

    it('should reject files that are too large', () => {
      const largeFile = new MockFile('test.png', 'image/png', 6 * 1024 * 1024); // 6MB
      const result = validateImageFile(largeFile, { required: true });
      expect(result).toBe('Image size must be less than 5MB');
    });

    it('should reject non-File objects', () => {
      const result = validateImageFile('not-a-file' as any, { required: true });
      expect(result).toBe('Please select a valid image file');
    });
  });
});
